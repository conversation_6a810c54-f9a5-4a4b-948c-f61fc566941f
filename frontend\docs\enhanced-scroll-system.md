# Enhanced Scroll System Documentation

## Overview

The Enhanced Scroll System provides comprehensive scroll handling for the TransactionView component with smart status bar interaction, action card visibility detection, and smooth navigation features. The system is designed to be performant, accessible, and mobile-optimized.

## Architecture

The system consists of several interconnected composables:

### Core Composables

1. **`useEnhancedScroll`** - Main scroll handling composable
2. **`useActionCardVisibility`** - Advanced intersection observer system
3. **`useSmoothScrollNavigation`** - Smooth scroll navigation with accessibility
4. **`useMobileScrollOptimization`** - Mobile-specific optimizations
5. **`useScrollPerformance`** - Performance optimization and monitoring

## Features

### Smart Status Bar Interaction
- **Scroll Direction Detection**: Intelligent detection of scroll direction with hysteresis
- **Adaptive Shrinking**: Status bar shrinks on scroll down, expands on scroll up
- **Smooth Transitions**: CSS transitions for seamless visual changes
- **Navigation Helper**: Icon appears when action cards are out of view

### Action Card Visibility Detection
- **Multiple Thresholds**: Granular visibility detection (0%, 10%, 25%, 50%, 75%, 100%)
- **Stability Detection**: Prevents rapid state changes with stability delays
- **Performance Optimized**: Uses pooled intersection observers
- **Status Bar Compensation**: Accounts for status bar height in calculations

### Smooth Navigation System
- **Accessibility Support**: Respects `prefers-reduced-motion` settings
- **Screen Reader Support**: Announces navigation actions
- **Focus Management**: Proper focus handling after scroll
- **Visual Highlighting**: Animated highlights for scroll targets

### Mobile Optimization
- **Touch Event Handling**: Enhanced touch scroll behavior
- **Momentum Scrolling**: iOS Safari momentum scrolling support
- **Viewport Compensation**: Dynamic viewport height handling
- **Battery Optimization**: Reduces animations on low battery

### Performance Features
- **Adaptive Throttling**: Adjusts throttle rates based on device performance
- **Observer Pooling**: Reuses intersection observers for efficiency
- **Memory Management**: Automatic cleanup and leak prevention
- **Frame Rate Monitoring**: Real-time performance metrics

## Usage

### Basic Setup

```typescript
import { useEnhancedScroll } from '@/composables/useEnhancedScroll'

const {
  isStatusBarShrunk,
  scrollToActionCard,
  observeActionCard,
  actionCardVisibility,
  isAnyActionCardVisible
} = useEnhancedScroll(
  computed(() => scrollContainerRef.value),
  {
    scroll: {
      debug: process.env.NODE_ENV === 'development',
      thresholds: {
        shrinkThreshold: 80,
        expandThreshold: 40,
        hysteresis: 20
      }
    },
    actionCardVisibility: {
      rootMargin: '-60px 0px -80px 0px',
      minVisibilityPercent: 25
    }
  }
)
```

### Action Card Integration

```typescript
// In ActionCard.vue
import { useActionCardVisibility } from '@/composables/useActionCardVisibility'

const {
  observe,
  unobserve,
  isCardVisible,
  isCardFullyVisible
} = useActionCardVisibility({
  rootMargin: '-60px 0px -80px 0px',
  minVisibilityPercent: 25,
  stabilityDelay: 150
})

onMounted(async () => {
  await nextTick()
  if (actionCardRef.value) {
    observe(actionCardRef.value, props.item.id)
  }
})

onUnmounted(() => {
  unobserve(props.item.id)
})
```

### Status Bar Enhancement

```typescript
// In TheSmartStatusBar.vue
const handleStatusBarClick = async () => {
  if (shouldShowNavigationHint.value && pinnedAction.value) {
    await transactionalChatStore.scrollToActionCard(pinnedAction.value.cardId)
  }
}
```

## Configuration Options

### Scroll Options

```typescript
interface ScrollOptions {
  thresholds?: {
    shrinkThreshold: number    // Default: 80px
    expandThreshold: number    // Default: 40px
    hysteresis: number        // Default: 20px
    minScrollDelta: number    // Default: 5px
  }
  throttleDelay?: number      // Default: 16ms (~60fps)
  debounceDelay?: number      // Default: 200ms
  debug?: boolean            // Default: false
}
```

### Visibility Options

```typescript
interface ActionCardVisibilityOptions {
  thresholds?: number[]              // Default: [0, 0.1, 0.25, 0.5, 0.75, 1.0]
  rootMargin?: string               // Default: '-60px 0px -80px 0px'
  minVisibilityPercent?: number     // Default: 25
  stabilityDelay?: number           // Default: 150ms
  debug?: boolean                   // Default: false
}
```

### Navigation Options

```typescript
interface ScrollNavigationOptions {
  behavior?: ScrollBehavior         // Default: 'smooth'
  block?: ScrollLogicalPosition     // Default: 'center'
  inline?: ScrollLogicalPosition    // Default: 'nearest'
  highlightClass?: string          // Default: 'scroll-highlight'
  highlightDuration?: number       // Default: 2000ms
  focus?: boolean                  // Default: false
  announce?: boolean               // Default: false
}
```

## RTL/LTR Support

The system automatically detects and adapts to RTL (Persian) and LTR (English) layouts:

- **Scroll Positioning**: Adjusts inline scroll positioning for RTL
- **Navigation Icons**: Proper positioning in status bar
- **Touch Handling**: RTL-aware touch event processing

## Performance Considerations

### Throttling Strategy
- Base throttle: 16ms (~60fps)
- Adaptive throttling based on device performance
- Maximum throttle: 100ms (~10fps) for low-performance devices

### Memory Management
- Automatic cleanup of intersection observers
- Pooled observers to reduce memory usage
- Proper event listener cleanup on unmount

### Battery Optimization
- Reduces animation frequency on low battery
- Disables smooth scrolling when battery < 20%
- Monitors battery level changes

## Testing

### Unit Tests
- Comprehensive test coverage for all composables
- Mock implementations for browser APIs
- Performance and edge case testing

### Integration Tests
- End-to-end scroll behavior testing
- Cross-browser compatibility testing
- Mobile device testing

## Browser Support

- **Modern Browsers**: Full feature support
- **iOS Safari**: Enhanced momentum scrolling
- **Android Chrome**: Optimized touch handling
- **Fallbacks**: Graceful degradation for older browsers

## Troubleshooting

### Common Issues

1. **Status Bar Not Shrinking**
   - Check scroll container reference
   - Verify threshold configuration
   - Enable debug mode for diagnostics

2. **Action Cards Not Detected**
   - Ensure proper `data-card-id` attributes
   - Check intersection observer root margin
   - Verify element is in DOM when observing

3. **Performance Issues**
   - Enable performance monitoring
   - Check throttle/debounce settings
   - Monitor frame rate metrics

### Debug Mode

Enable debug logging:

```typescript
const scrollSystem = useEnhancedScroll(target, {
  scroll: { debug: true },
  actionCardVisibility: { debug: true }
})
```

Debug output includes:
- Scroll direction changes
- Status bar state transitions
- Action card visibility changes
- Performance metrics
- Error conditions

## Migration Guide

### From Legacy Scroll System

1. Replace manual scroll event listeners with `useEnhancedScroll`
2. Update action card components to use `useActionCardVisibility`
3. Remove custom throttling/debouncing code
4. Update status bar to use new navigation features

### Breaking Changes

- Removed manual scroll event handling
- Changed action card visibility detection API
- Updated status bar shrinking thresholds
- Modified scroll navigation method signatures

## Future Enhancements

- Virtual scrolling support
- Gesture-based navigation
- Advanced performance analytics
- Custom scroll animations
- Accessibility improvements
