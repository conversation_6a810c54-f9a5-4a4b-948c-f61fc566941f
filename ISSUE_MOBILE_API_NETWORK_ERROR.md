# Issue: Mobile Cannot Access Backend API (LAN)

## Summary
When accessing the MUNygo frontend from a mobile device on the same LAN, the app loads and the socket connection indicator is green, but all API calls (offers, notifications, etc.) fail with network errors. The same app works fine on the host machine (localhost and LAN IP).

## Key Findings
- **Frontend API requests are sent to `http://localhost:3000` instead of the LAN IP.**
- **Socket.IO connects successfully to `http://************:3000` on mobile.**
- **.env is set to:**
  ```
  VITE_API_BASE_URL=http://************:3000/api
  VITE_BACKEND_URL=http://************:3000
  ```
- **Frontend code uses `apiClient` with `VITE_API_BASE_URL` for all offer-related API calls.**
- **No hardcoded `localhost` found in offerService.ts or other main service files.**
- **Incognito mode and cache clearing do not resolve the issue.**
- **Frontend logs (from mobile) show failed requests to `localhost:3000`, not the LAN IP.**
- **Restarting the frontend server does not resolve the issue.**
- **No other `.env.*` files found, but possible Vite is not picking up changes.**

## Steps Taken
1. Updated `.env` to use LAN IP for API base URLs.
2. Patched all service files to use env variables and fallback to LAN IP.
3. Restarted frontend and backend servers after changes.
4. Cleared browser cache and used incognito mode on mobile.
5. Verified socket connection works on mobile.
6. Verified backend CORS allows LAN and localhost origins.
7. **Modified `frontend/package.json` to explicitly set `VITE_API_BASE_URL` and `VITE_BACKEND_URL` in the `dev` script (Windows compatible).**

## Suspected Causes
- Vite dev server is not picking up `.env` changes (possible caching or multiple env files).
- Some frontend code or build artifact is still using old `localhost` config.
- Mobile browser is not loading the latest frontend build.

## Next Steps
- **Stop the frontend dev server completely.**
- **Restart the frontend dev server using `npm run dev`.**
- **Clear browser cache on mobile and retest.**
- If issue persists, investigate Vite config and build artifacts for stale values.

---
**Logs and error details attached in `logs.txt`.**

---
**Please add any further findings or suggestions below.**
---