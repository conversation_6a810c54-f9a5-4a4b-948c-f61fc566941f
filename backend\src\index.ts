import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { logger as honoLogger } from 'hono/logger';
import { cors } from 'hono/cors';
import { verify } from 'hono/jwt';
import { Server as SocketIOServer, Server } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import { getPrismaClient, disconnectDatabase } from './utils/database';
import authRoutes from './routes/auth';
import createOfferRoutes from './routes/offer';
import notificationRoutes from './routes/notificationRoutes';
import { createChatRouter } from './routes/chatRoutes';
import createTransactionRoutes from './routes/transactionRoutes'; // Import factory function
import createPayerNegotiationRoutes from './routes/payerNegotiationRoutes';
import createDebugRoutes from './routes/debugRoutes'; // Import debug routes factory
import { createAiRoutes } from './routes/aiRoutes'; // Import AI routes
import createTagRoutes, { createAdminTagRoutes } from './routes/tagRoutes'; // Import tag routes
import createMatchRoutes from './routes/matchRoutes'; // Import matching routes factory
import { createTransactionalChatRoutes } from './routes/transactionalChatRoutes'; // Import transactional chat routes
import createPaymentMethodRoutes from './routes/paymentMethods'; // Import payment methods routes
import { createPaymentInfoRoutes } from './routes/paymentInfoRoutes'; // Import payment info routes
import { initializeEmailTransporter } from './services/email';
import { NotificationService } from './services/notificationService';
import { ChatService } from './services/chatService';
import { TransactionService } from './services/transactionService';
import { PayerNegotiationService } from './services/payerNegotiationService';
import { ClientLogService } from './services/clientLogService'; // Import client log service
import { TagService } from './services/tagService'; // Import tag service
import { MatchingService } from './services/matchingService'; // Import matching service
import { MatchingJobService } from './services/matchingJobService'; // Import matching job service
import { ConsoleLogger, ILogger } from './utils/logger';
import { CHAT_MESSAGE_SEND } from './types/socketEvents';
import dotenv from 'dotenv';
import { AuthVariables } from './middleware/auth';

dotenv.config();

interface AppEnvVars {
  io: Server;
  notificationService: NotificationService;
  chatService: ChatService;
  transactionService: TransactionService;
}

const app = new Hono<{ Variables: AppEnvVars & AuthVariables }>();
const prisma = getPrismaClient(); // Use shared PrismaClient singleton
const consoleAppLogger: ILogger = new ConsoleLogger();

// Export app for testing
export { app };

const JWT_SECRET = process.env.JWT_SECRET || 'your-default-secret-key';

initializeEmailTransporter().catch(console.error);

const port = parseInt(process.env.PORT || '3000', 10);

// Only start server if this file is run directly, not imported
let server: any;
export let io: any;

if (require.main === module) {
  server = serve({
    fetch: app.fetch,
    port,
    hostname: '0.0.0.0'
  });

  // Set up Socket.IO
  io = new SocketIOServer(server, {
    cors: {
      origin: process.env.FRONTEND_URL || 'http://localhost:5173',
      methods: ['GET', 'POST']
    }
  });
} else {
  // Create a mock io for testing/import scenarios
  io = {
    to: () => ({ emit: () => {} }),
    emit: () => {},
    on: () => {},
    use: () => {}
  };
}

// Initialize services in proper dependency order
const notificationServiceInstance = new NotificationService(io);
export const chatServiceInstance = new ChatService(io);

// Create MatchingService first (needed by TransactionService)
const matchingServiceInstance = new MatchingService(
  prisma,
  io,
  notificationServiceInstance
);

// Create TransactionService with MatchingService dependency
const transactionServiceInstance = new TransactionService(io, notificationServiceInstance, chatServiceInstance, matchingServiceInstance);
const clientLogServiceInstance = new ClientLogService(); // Initialize client log service
const tagServiceInstance = new TagService(prisma); // Initialize tag service with prisma

// PayerNegotiationService with all dependencies
const payerNegotiationServiceInstance = new PayerNegotiationService(
  prisma,
  chatServiceInstance,
  io,
  consoleAppLogger,
  transactionServiceInstance,
  notificationServiceInstance
);

// MatchingJobService for background processing
const matchingJobServiceInstance = new MatchingJobService(
  prisma,
  matchingServiceInstance,
  consoleAppLogger
);

// Middleware
app.use('*', honoLogger());
app.use('*', cors({
  origin: [
    'http://************:5173',
    'http://localhost:5173'
  ],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

app.use('*', async (c, next) => {
  c.set('io', io);
  c.set('notificationService', notificationServiceInstance);
  c.set('chatService', chatServiceInstance);
  c.set('transactionService', transactionServiceInstance);
  await next();
});

// Basic health check endpoint
app.get('/', (c) => c.json({ status: 'ok', message: 'MUNygo API is running' }));

// Comprehensive health check endpoint for Docker
app.get('/health', async (c) => {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    return c.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      database: 'connected',
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    consoleAppLogger.error('Health check failed:', error);
    return c.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 503);
  }
});

// API Health check endpoint
app.get('/api/health', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Mount routes
app.route('/api/auth', authRoutes);
app.route('/api/offers', createOfferRoutes(io, notificationServiceInstance, matchingServiceInstance));
app.route('/api/notifications', notificationRoutes);
app.route('/api/tags', createTagRoutes(tagServiceInstance)); // Mount tag routes
app.route('/api/admin/tags', createAdminTagRoutes(tagServiceInstance)); // Mount admin tag routes
app.route('/api/debug', createDebugRoutes(clientLogServiceInstance, prisma)); // Mount debug routes with dual-write capability
app.route('/api/ai', createAiRoutes()); // Mount AI routes for voice-powered bug reporting

// Use factory functions for routes that need service injection
app.route('/api/transactions', createTransactionRoutes(payerNegotiationServiceInstance));
app.route('/api/payer-negotiation', createPayerNegotiationRoutes(payerNegotiationServiceInstance));
app.route('/api/matches', createMatchRoutes(matchingServiceInstance)); // Mount matching routes
app.route('/api/transactional-chat', createTransactionalChatRoutes(prisma, payerNegotiationServiceInstance)); // Mount transactional chat routes with shared PrismaClient
app.route('/api/payment-methods', createPaymentMethodRoutes(prisma)); // Mount payment methods routes
app.route('/api/payment-info', createPaymentInfoRoutes(prisma)); // Mount payment info routes with shared PrismaClient

// Create chat router with transactionService and mount it
const chatRouter = createChatRouter(transactionServiceInstance);
app.route('/api/chat', chatRouter);

// Export getters for services if needed elsewhere
export const getNotificationService = () => {
  if (!notificationServiceInstance) {
    consoleAppLogger.error("NotificationService not initialized during getNotificationService call.");
    throw new Error("NotificationService has not been initialized.");
  }
  return notificationServiceInstance;
};

export const getTransactionService = () => {
  if (!transactionServiceInstance) {
    throw new Error("TransactionService not initialized.");
  }
  return transactionServiceInstance;
};

export const getPayerNegotiationService = () => {
    if (!payerNegotiationServiceInstance) {
        throw new Error("PayerNegotiationService not initialized.");
    }
    return payerNegotiationServiceInstance;
};

export const getMatchingService = () => {
    if (!matchingServiceInstance) {
        throw new Error("MatchingService not initialized.");
    }
    return matchingServiceInstance;
};

export const getMatchingJobService = () => {
    if (!matchingJobServiceInstance) {
        throw new Error("MatchingJobService not initialized.");
    }
    return matchingJobServiceInstance;
};

// Only set up Socket.IO handlers if server is actually running
if (require.main === module) {
  // Socket.IO authentication middleware
  io.use(async (socket: any, next: any) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error: Token required'));
      }

      const decoded = await verify(token, JWT_SECRET) as { userId: string; exp?: number };
      if (!decoded || !decoded.userId) {
        return next(new Error('Authentication error: Invalid token payload'));
      }

      // Check if token is expired (additional check for clarity)
      if (decoded.exp && decoded.exp < Math.floor(Date.now() / 1000)) {
        return next(new Error('Authentication error: Token expired'));
      }

      socket.data.userId = decoded.userId;
      await socket.join(decoded.userId);
      consoleAppLogger.info(`Socket authenticated and joined room for user: ${decoded.userId}`);
      return next();
    } catch (err: any) {
      consoleAppLogger.error('Socket authentication error:', err);
      
      // Provide more specific error messages based on the error type
      if (err.message && err.message.includes('expired')) {
        return next(new Error('Authentication error: Token expired'));
      } else if (err.message && err.message.includes('invalid')) {
        return next(new Error('Authentication error: Invalid token'));
      } else {
        return next(new Error('Authentication error: Invalid token'));
      }
    }
  });

// Socket.IO connection handling
io.on('connection', (socket: any) => {
  consoleAppLogger.info(`✅ User connected: ${socket.data.userId} via ${socket.conn.transport.name} (ID: ${socket.id})`);

  // Monitor transport changes
  socket.conn.on('upgrade', () => {
    consoleAppLogger.info(`⬆️ User ${socket.data.userId} upgraded to: ${socket.conn.transport.name}`);
  });

  socket.conn.on('upgradeError', (error: any) => {
    consoleAppLogger.info(`⬇️ User ${socket.data.userId} upgrade failed, using: ${socket.conn.transport.name}`);
  });

  socket.on(CHAT_MESSAGE_SEND, (payload: any) => {
    chatServiceInstance.handleChatMessageSend(socket, payload);
  });

  socket.on('disconnect', (reason: any) => {
    consoleAppLogger.info(`🔌 User disconnected: ${socket.data.userId} (ID: ${socket.id}), reason: ${reason}`);
    
    // Enhanced disconnect reason logging
    if (reason === 'client namespace disconnect') {
      consoleAppLogger.info(`User ${socket.data.userId} disconnected voluntarily`);
    } else if (reason === 'transport close') {
      consoleAppLogger.info(`User ${socket.data.userId} lost connection (transport closed)`);
    } else if (reason === 'server namespace disconnect') {
      consoleAppLogger.info(`User ${socket.data.userId} disconnected by server`);
    }
  });

  socket.on('JOIN_TRANSACTION_ROOM', (transactionId: string) => {
    consoleAppLogger.info(`User ${socket.id} joining transaction room ${transactionId}`);
    socket.join(transactionId);
  });

  socket.on('LEAVE_TRANSACTION_ROOM', (transactionId: string) => {    consoleAppLogger.info(`User ${socket.id} leaving transaction room ${transactionId}`);
    socket.leave(transactionId);
  });
});

  consoleAppLogger.info(`Server is running on port ${port}`);

  // Start the automatic matching job service
  if (process.env.NODE_ENV !== 'test') {
    matchingJobServiceInstance.start();
    consoleAppLogger.info('🔄 Automatic matching job service started');
  }
}

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  consoleAppLogger.info('SIGTERM received, shutting down gracefully');
  matchingJobServiceInstance.stop();
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGINT', async () => {
  consoleAppLogger.info('SIGINT received, shutting down gracefully');
  matchingJobServiceInstance.stop();
  await disconnectDatabase();
  process.exit(0);
});
