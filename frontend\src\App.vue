<template>
  <n-config-provider :theme="themeStore.naiveTheme" :theme-overrides="themeStore.themeOverrides">
    <n-global-style />
    <n-dialog-provider>
      <n-message-provider>
        <n-notification-provider :placement="notificationPlacement">
          <!-- Animated background particles -->
          <div class="bg-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
          </div>
          <AppContent />
        </n-notification-provider>
      </n-message-provider>
    </n-dialog-provider>
    <!-- Stagewise dev toolbar - only appears in development mode -->
    <StagewiseDev />
  </n-config-provider>
</template>

<script setup lang="ts">
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  NGlobalStyle
} from 'naive-ui';
import AppContent from '@/components/AppContent.vue';
import StagewiseDev from '@/components/StagewiseDev.vue';
import { watch, onMounted, computed } from 'vue';
import { useRtl } from '@/utils/rtl';
import { useThemeStore } from '@/stores/theme';

const { direction } = useRtl();
const themeStore = useThemeStore();

// Compute notification placement based on direction
const notificationPlacement = computed(() => {
  return direction.value === 'rtl' ? 'top-left' : 'top-right';
});

// Initialize theme on app start
onMounted(() => {
  themeStore.initializeTheme();
});

// Update document direction when language changes
watch(direction, (newDir) => {
  document.documentElement.setAttribute('dir', newDir);
}, { immediate: true });
</script>

<style>
/* Global styles for DreamNavBar layout */
:root {
  /* Light theme */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #4ecdc4;
  --warning-color: #ffe066;
  --error-color: #ff6b6b;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --bg-primary: rgba(255, 255, 255, 0.95);
  --bg-secondary: rgba(247, 250, 252, 0.8);
  --border-color: rgba(226, 232, 240, 0.8);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --blur-strength: 20px;
}

[data-theme="dark"] {
  --primary-color: #9f7aea;
  --secondary-color: #667eea;
  --accent-color: #ed64a6;
  --success-color: #38b2ac;
  --warning-color: #ecc94b;
  --error-color: #f56565;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --bg-primary: rgba(26, 32, 44, 0.95);
  --bg-secondary: rgba(45, 55, 72, 0.8);
  --border-color: rgba(74, 85, 104, 0.8);
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.4);
}

html, body, #app {
  margin: 0;
  padding: 0;
  height: 100vh; /* Fixed: Use height instead of min-height to prevent page overflow */
  overflow: hidden; /* Prevent any scrolling at the document level */
  font-family: 'Inter', 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', 'Segoe UI', -apple-system, BlinkMacSystemFont, Roboto, sans-serif;
}

/* Persian/RTL specific font optimization */
[dir="rtl"] {
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6; /* Slightly increased line height for Persian text readability */
  font-feature-settings: "liga" 1, "kern" 1; /* Enable ligatures and kerning for better Persian text */
  text-rendering: optimizeLegibility;
}

/* Persian text elements */
[dir="rtl"] *,
[lang="fa"] *,
.persian-text {
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Persian text rendering optimization */
[dir="rtl"] .n-button,
[dir="rtl"] .n-input,
[dir="rtl"] .n-select,
[dir="rtl"] .n-card,
[dir="rtl"] .n-modal,
[dir="rtl"] .n-dropdown,
[dir="rtl"] .n-popover,
[dir="rtl"] .n-tooltip,
[lang="fa"] .n-button,
[lang="fa"] .n-input,
[lang="fa"] .n-select,
[lang="fa"] .n-card,
[lang="fa"] .n-modal,
[lang="fa"] .n-dropdown,
[lang="fa"] .n-popover,
[lang="fa"] .n-tooltip {
  font-family: 'Vazirmatn', 'Vazir', 'Tahoma', 'Iran Sans', -apple-system, BlinkMacSystemFont, sans-serif !important;
}

/* Persian font weight and spacing optimizations */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6,
[lang="fa"] h1, [lang="fa"] h2, [lang="fa"] h3, [lang="fa"] h4, [lang="fa"] h5, [lang="fa"] h6 {
  font-weight: 600; /* Slightly bolder for Persian headings */
  letter-spacing: 0; /* Remove letter spacing for Persian */
}

[dir="rtl"] p, [dir="rtl"] span, [dir="rtl"] div,
[lang="fa"] p, [lang="fa"] span, [lang="fa"] div {
  letter-spacing: 0; /* Remove letter spacing for Persian text */
}

/* Persian button text optimization */
[dir="rtl"] button, [dir="rtl"] .n-button,
[lang="fa"] button, [lang="fa"] .n-button {
  font-weight: 500; /* Optimal weight for Persian buttons */
  letter-spacing: 0;
}

body {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
  color: var(--text-primary);
  transition: all 0.3s ease;
  overflow: hidden; /* Prevent both horizontal and vertical scrolling at body level */
}

.main-content {
  margin-top: 110px; /* Space for DreamNavBar */
  height: calc(100vh - 110px); /* Fixed: Use height instead of min-height */
  overflow-y: auto; /* Allow scrolling within main content only */
  padding: 20px;
}

.app-container {
  height: 100vh; /* Fixed: Use height instead of min-height */
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden; /* Prevent scrolling at app container level */
}

/* Remove old navbar styles */
.n-layout-header {
  display: none;
}

.n-layout-content {
  margin-top: 0;
  min-height: 100vh;
}

/* Animated background particles */
.bg-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { width: 80px; height: 80px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 120px; height: 120px; left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { width: 60px; height: 60px; left: 60%; animation-delay: 2s; }
.particle:nth-child(4) { width: 100px; height: 100px; left: 80%; animation-delay: 3s; }
.particle:nth-child(5) { width: 40px; height: 40px; left: 40%; animation-delay: 4s; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.5; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}
</style>

<style scoped>
/* Scoped styles from original App.vue can remain here or be moved if preferred */
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 64px;
}

.header-left {
  flex: 1;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 16px;
}
</style>
