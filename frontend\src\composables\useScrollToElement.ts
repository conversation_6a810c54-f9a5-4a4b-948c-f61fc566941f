/**
 * Composable for scrolling to elements and adding highlight effects
 * Provides better separation of concerns and easier testing
 */

export interface ScrollToElementOptions {
  behavior?: ScrollBehavior;
  block?: ScrollLogicalPosition;
  inline?: ScrollLogicalPosition;
  highlightClass?: string;
  highlightDuration?: number;
  retryDelay?: number;
  maxRetries?: number;
}

export interface UseScrollToElementReturn {
  scrollToElementBySelector: (selector: string, options?: ScrollToElementOptions) => Promise<boolean>;
  scrollToElementByTestId: (testId: string, options?: ScrollToElementOptions) => Promise<boolean>;
  scrollToElementByCardId: (cardId: string, options?: ScrollToElementOptions) => Promise<boolean>;
  addHighlightEffect: (element: Element, className: string, duration: number) => void;
}

export function useScrollToElement(): UseScrollToElementReturn {
  const defaultOptions: ScrollToElementOptions = {
    behavior: 'smooth',
    block: 'center',
    inline: 'nearest',
    highlightClass: 'highlight-card',
    highlightDuration: 2000,
    retryDelay: 100,
    maxRetries: 1
  };

  /**
   * Safely escape CSS selector values to prevent XSS and selector errors
   * Polyfill for CSS.escape if not available
   */
  const escapeCSSSelector = (value: string): string => {
    // Use native CSS.escape if available, otherwise use polyfill
    if (typeof CSS !== 'undefined' && CSS.escape) {
      return CSS.escape(value);
    }
    // Polyfill for CSS.escape
    return value.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, '\\$&');
  };

  /**
   * Add highlight effect to an element
   */
  const addHighlightEffect = (element: Element, className: string, duration: number): void => {
    element.classList.add(className);
    setTimeout(() => {
      element.classList.remove(className);
    }, duration);
  };

  /**
   * Scroll to element with retry mechanism
   */
  /**
   * Find the scrollable container (chat feed) to prevent page-level scrolling
   */
  const findScrollableContainer = (): HTMLElement | null => {
    // Look for the unified feed container first
    const feedContainer = document.querySelector('.unified-feed') as HTMLElement;
    if (feedContainer) {
      return feedContainer;
    }

    // Fallback to any element with overflow scroll
    const scrollableElements = document.querySelectorAll('[style*="overflow"], [class*="scroll"]');
    for (const el of scrollableElements) {
      const style = window.getComputedStyle(el as HTMLElement);
      if (style.overflowY === 'auto' || style.overflowY === 'scroll') {
        return el as HTMLElement;
      }
    }

    return null;
  };

  /**
   * Scroll element into view within the chat feed container only
   */
  const scrollElementIntoFeedView = (element: Element, options: ScrollToElementOptions): void => {
    const scrollContainer = findScrollableContainer();

    if (!scrollContainer) {
      // Fallback to regular scrollIntoView if no container found
      element.scrollIntoView({
        behavior: options.behavior,
        block: options.block,
        inline: options.inline
      });
      return;
    }

    // Calculate positions relative to the scroll container
    const elementRect = element.getBoundingClientRect();
    const containerRect = scrollContainer.getBoundingClientRect();

    // Calculate the target scroll position
    let targetScrollTop = scrollContainer.scrollTop;

    switch (options.block) {
      case 'start':
        targetScrollTop += elementRect.top - containerRect.top;
        break;
      case 'center':
        targetScrollTop += elementRect.top - containerRect.top - (containerRect.height / 2) + (elementRect.height / 2);
        break;
      case 'end':
        targetScrollTop += elementRect.bottom - containerRect.bottom;
        break;
      case 'nearest':
      default:
        // Only scroll if element is not fully visible
        if (elementRect.top < containerRect.top) {
          targetScrollTop += elementRect.top - containerRect.top;
        } else if (elementRect.bottom > containerRect.bottom) {
          targetScrollTop += elementRect.bottom - containerRect.bottom;
        }
        break;
    }

    // Perform the scroll within the container only
    scrollContainer.scrollTo({
      top: targetScrollTop,
      behavior: options.behavior
    });
  };

  const scrollToElementWithRetry = async (
    selector: string,
    options: ScrollToElementOptions = {}
  ): Promise<boolean> => {
    const mergedOptions = { ...defaultOptions, ...options };

    const attemptScroll = (): boolean => {
      const element = document.querySelector(selector);
      if (element) {
        // Use our custom scroll function to prevent page-level scrolling
        scrollElementIntoFeedView(element, mergedOptions);

        // Add highlight effect if specified
        if (mergedOptions.highlightClass) {
          addHighlightEffect(element, mergedOptions.highlightClass, mergedOptions.highlightDuration!);
        }

        return true;
      }
      return false;
    };

    // Try immediately
    if (attemptScroll()) {
      return true;
    }

    // Retry after delay if configured
    if (mergedOptions.maxRetries! > 0) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const success = attemptScroll();
          if (!success) {
            console.warn(`⚠️ Could not find element with selector: ${selector}`);
          }
          resolve(success);
        }, mergedOptions.retryDelay);
      });
    }

    console.warn(`⚠️ Could not find element with selector: ${selector}`);
    return false;
  };

  /**
   * Scroll to element by CSS selector
   */
  const scrollToElementBySelector = async (
    selector: string,
    options?: ScrollToElementOptions
  ): Promise<boolean> => {
    return scrollToElementWithRetry(selector, options);
  };

  /**
   * Scroll to element by data-testid attribute
   */
  const scrollToElementByTestId = async (
    testId: string,
    options?: ScrollToElementOptions
  ): Promise<boolean> => {
    const selector = `[data-testid="${escapeCSSSelector(testId)}"]`;
    return scrollToElementWithRetry(selector, options);
  };

  /**
   * Scroll to element by data-card-id attribute
   */
  const scrollToElementByCardId = async (
    cardId: string,
    options?: ScrollToElementOptions
  ): Promise<boolean> => {
    const selector = `[data-card-id="${escapeCSSSelector(cardId)}"]`;
    return scrollToElementWithRetry(selector, options);
  };

  return {
    scrollToElementBySelector,
    scrollToElementByTestId,
    scrollToElementByCardId,
    addHighlightEffect
  };
}
