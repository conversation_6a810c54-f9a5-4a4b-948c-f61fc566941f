# Persian Wordings for TheSmartStatusBar.vue

This report documents the Persian sentences shown in the status bar titles for both expanded and shrunk modes in the `TheSmartStatusBar.vue` component, based on the logic and localization defined in `transactionalChat.json`.

---

## Expanded Mode (stepTitle)

| Step                        | User's Turn                                   | Waiting for Other User                      |
|-----------------------------|-----------------------------------------------|---------------------------------------------|
| paymentInfo                 | ارائه اطلاعات پرداخت                         | در انتظار افزودن اطلاعات پرداخت توسط {name} |
| negotiation                 | تعیین پرداخت‌کننده اول                       | در انتظار تصمیم {name}                      |
| makePayment                 | اقدام شما: ارسال {amount}                    | به زودی {amount} دریافت خواهید کرد          |
| confirmReceipt              | اقدام شما: تأیید دریافت {amount}             | در انتظار تأیید دریافت توسط {name}          |
| makeSecondPayment           | اقدام شما: ارسال {amount}                    | به زودی {amount} دریافت خواهید کرد          |
| confirmFirstPaymentReceipt  | اقدام شما: تأیید نهایی دریافت {amount}       | در انتظار تأیید نهایی توسط {name}           |

---

## Shrunk Mode (shrunkStepTitle)

| Step                        | User's Turn                   | Waiting for Other User      |
|-----------------------------|-------------------------------|-----------------------------|
| paymentInfo                 | افزودن اطلاعات پرداخت         | انتظار {name}               |
| negotiation                 | تعیین پرداخت‌کننده اول         | انتظار {name}               |
| makePayment                 | ارسال {amount}                | در حال دریافت               |
| confirmReceipt              | تأیید دریافت                  | انتظار {name}               |
| makeSecondPayment           | ارسال {amount}                | در حال دریافت               |
| confirmFirstPaymentReceipt  | تأیید نهایی دریافت            | انتظار {name}               |

---

**Notes:**
- `{name}` and `{amount}` are dynamically replaced with the other user's name and the transaction amount, respectively.
- These wordings are sourced from the Persian locale file and mapped in the component logic for each transaction step and user context.

---

**Generated by GitHub Copilot on July 11, 2025**
