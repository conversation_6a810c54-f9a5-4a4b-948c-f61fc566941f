/**
 * Mobile Scroll Fix Utilities
 * 
 * This module provides utilities to prevent unwanted page scrolling on mobile devices
 * when interacting with specific UI components like headers, status bars, and input fields.
 * 
 * The fixes address the browser intervention warnings:
 * "Ignored attempt to cancel a touchmove event with cancelable=false"
 */

export interface TouchEventHandlers {
  handleTouchStart: (event: TouchEvent) => void
  handleTouchMove: (event: TouchEvent) => void
  handleTouchEnd: (event: TouchEvent) => void
}

/**
 * Creates touch event handlers that prevent scroll bubbling
 * @param options Configuration options for touch handling
 * @returns Object with touch event handlers
 */
export function createMobileTouchHandlers(options: {
  preventVerticalScroll?: boolean
  preventHorizontalScroll?: boolean
  allowTextSelection?: boolean
} = {}): TouchEventHandlers {
  const {
    preventVerticalScroll = true,
    preventHorizontalScroll = false,
    allowTextSelection = true
  } = options

  let touchStartY = 0
  let touchStartX = 0

  const handleTouchStart = (event: TouchEvent) => {
    const touch = event.touches[0]
    touchStartY = touch.clientY
    touchStartX = touch.clientX
    
    // Always prevent bubbling to parent containers
    event.stopPropagation()
  }

  const handleTouchMove = (event: TouchEvent) => {
    const touch = event.touches[0]
    const deltaY = Math.abs(touch.clientY - touchStartY)
    const deltaX = Math.abs(touch.clientX - touchStartX)
    
    // Determine if this is a scroll gesture
    const isVerticalScroll = deltaY > deltaX && deltaY > 10
    const isHorizontalScroll = deltaX > deltaY && deltaX > 10
    
    // Prevent based on configuration
    if ((preventVerticalScroll && isVerticalScroll) || 
        (preventHorizontalScroll && isHorizontalScroll)) {
      event.preventDefault()
    }
    
    // Always prevent bubbling
    event.stopPropagation()
  }

  const handleTouchEnd = (event: TouchEvent) => {
    // Clean up and prevent bubbling
    event.stopPropagation()
    touchStartY = 0
    touchStartX = 0
  }

  return {
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd
  }
}

/**
 * Applies non-passive touch event listeners to an element
 * @param element The DOM element to attach listeners to
 * @param handlers Touch event handlers
 * @returns Cleanup function to remove listeners
 */
export function applyMobileTouchFix(
  element: HTMLElement,
  handlers: TouchEventHandlers
): () => void {
  // Use non-passive listeners to make events cancelable
  element.addEventListener('touchstart', handlers.handleTouchStart, { passive: false })
  element.addEventListener('touchmove', handlers.handleTouchMove, { passive: false })
  element.addEventListener('touchend', handlers.handleTouchEnd, { passive: false })

  // Return cleanup function
  return () => {
    element.removeEventListener('touchstart', handlers.handleTouchStart)
    element.removeEventListener('touchmove', handlers.handleTouchMove)
    element.removeEventListener('touchend', handlers.handleTouchEnd)
  }
}

/**
 * Predefined touch handlers for common UI components
 */
export const MOBILE_TOUCH_PRESETS = {
  // For headers and status bars - prevent all scrolling
  HEADER_BAR: createMobileTouchHandlers({
    preventVerticalScroll: true,
    preventHorizontalScroll: true,
    allowTextSelection: false
  }),
  
  // For input containers - prevent vertical scroll but allow text selection
  INPUT_CONTAINER: createMobileTouchHandlers({
    preventVerticalScroll: true,
    preventHorizontalScroll: false,
    allowTextSelection: true
  }),
  
  // For action bars - prevent all scrolling
  ACTION_BAR: createMobileTouchHandlers({
    preventVerticalScroll: true,
    preventHorizontalScroll: true,
    allowTextSelection: false
  })
}

/**
 * CSS classes that should be applied to prevent mobile scroll issues
 */
export const MOBILE_SCROLL_CSS = {
  // Completely prevent touch scrolling
  NO_SCROLL: {
    'touch-action': 'none',
    'overscroll-behavior': 'contain',
    'contain': 'layout style'
  },
  
  // Allow only specific touch actions
  VERTICAL_ONLY: {
    'touch-action': 'pan-y',
    'overscroll-behavior': 'contain'
  },
  
  // Allow text input but prevent scroll
  INPUT_SAFE: {
    'touch-action': 'manipulation',
    'overscroll-behavior': 'contain'
  }
} as const

/**
 * Diagnostic function to check if mobile scroll fixes are working
 */
export function diagnoseMobileScrollIssues(): {
  hasPassiveListeners: boolean
  supportsTouchAction: boolean
  supportsOverscrollBehavior: boolean
  recommendations: string[]
} {
  const recommendations: string[] = []
  
  // Check for passive listener support
  let hasPassiveListeners = false
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get() {
        hasPassiveListeners = true
        return false
      }
    })
    window.addEventListener('test', () => {}, opts)
    window.removeEventListener('test', () => {}, opts)
  } catch (e) {
    hasPassiveListeners = false
  }
  
  // Check CSS support
  const supportsTouchAction = CSS.supports('touch-action', 'none')
  const supportsOverscrollBehavior = CSS.supports('overscroll-behavior', 'contain')
  
  // Generate recommendations
  if (!hasPassiveListeners) {
    recommendations.push('Browser does not support passive event listeners')
  }
  
  if (!supportsTouchAction) {
    recommendations.push('Browser does not support touch-action CSS property')
  }
  
  if (!supportsOverscrollBehavior) {
    recommendations.push('Browser does not support overscroll-behavior CSS property')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('All mobile scroll prevention features are supported')
  }
  
  return {
    hasPassiveListeners,
    supportsTouchAction,
    supportsOverscrollBehavior,
    recommendations
  }
}
