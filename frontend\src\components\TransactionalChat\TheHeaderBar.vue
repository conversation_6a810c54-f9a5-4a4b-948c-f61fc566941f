<script setup lang="ts">

import { computed, onMounted, onBeforeUnmount, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useTransactionalChatStore } from '@/stores/transactionalChat/transactionalChatStore'
import { useThemeStore } from '@/stores/themeStore'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import CustomTooltip from '@/components/ui/CustomTooltip.vue'

const { t } = useI18n()
const router = useRouter()
const transactionalChatStore = useTransactionalChatStore()
const themeStore = useThemeStore()

// Computed properties
const otherUser = computed(() => transactionalChatStore.otherUser)
const transactionDetails = computed(() => transactionalChatStore.transactionDetails)
const currentStep = computed(() => transactionalChatStore.currentStep)

// Transaction summary for header (e.g., 'خرید ۵۰۰ CAD' or 'فروش ۵۰۰ CAD')
type TransactionDetailsModern = {
  offerCreator: { id: string }
  otherUser: { id: string }
  finalCadAmount?: number | null
  amountToSend?: number
  amountToReceive?: number
  amount?: number
  [key: string]: any
}
type TransactionDetailsLegacy = {
  amountToSend: number
  amountToReceive: number
  currencyFrom: string
  currencyTo: string
  isUserFirstPayer: boolean
  otherUserPaymentDetails?: any
  userPaymentDetails?: any
  amount?: number
  [key: string]: any
}
type TransactionDetails = TransactionDetailsModern | TransactionDetailsLegacy

const toPersianDigits = (num: number | string): string => String(num).replace(/\d/g, (d: string) => '۰۱۲۳۴۵۶۷۸۹'[parseInt(d, 10)])

const authStore = useAuthStore()
const transactionSummary = computed(() => {
  const details = transactionDetails.value as TransactionDetails | undefined
  console.log('[header] transactionDetails:', details)
  if (!details || !authStore.user) return ''

  if (details.currencyFrom == 'CAD') {
    return t('transactionalChat.header.sellSummary', { amount: details.amountToSend, currency: 'CAD'})
  } else {
    return t('transactionalChat.header.buySummary', { amount: details.amountToReceive, currency: 'CAD'})
  }

  return ''
})

// Payment method info computed properties
const hasPaymentMethod = computed(() => {
  const details = transactionDetails.value
  return details?.userPaymentDetails !== null && details?.userPaymentDetails !== undefined
})

const isPaymentEditable = computed(() => {
  const step = currentStep.value
  // Allow editing before payment confirmation steps
  return step && step.key !== 'payment_confirmation' && step.key !== 'payment_completed'
})

const paymentMethodSummary = computed(() => {
  const details = transactionDetails.value
  if (!details?.userPaymentDetails) return null
  
  const method = details.userPaymentDetails
  return {
    currency: details.currencyTo,
    bankName: method.bankName || 'Bank details',
    accountNumber: method.accountNumber ? `***${method.accountNumber.slice(-4)}` : '',
    isComplete: method.validationStatus === 'complete'
  }
})

// Enhanced payment info for tooltip
const paymentInfoForTooltip = computed(() => {
  const details = transactionDetails.value
  if (!details?.userPaymentDetails) return null
  
  const method = details.userPaymentDetails
  return {
    currency: details.currencyTo,
    bankName: method.bankName || 'Bank details',
    accountNumber: method.accountNumber || 'Not provided',
    accountHolder: method.accountHolder,
    iban: method.iban,
    swiftCode: method.swiftCode,
    routingNumber: method.routingNumber,
    isComplete: method.validationStatus === 'complete',
    validationStatus: method.validationStatus || 'pending'
  }
})

const reputationColor = computed(() => {
  if (!otherUser.value) return 'var(--tc-text-muted)'
  
  const reputation = otherUser.value.reputation
  if (reputation >= 5) return 'var(--tc-reputation-5)'
  if (reputation >= 4) return 'var(--tc-reputation-4)'
  if (reputation >= 3) return 'var(--tc-reputation-3)'
  if (reputation >= 2) return 'var(--tc-reputation-2)'
  return 'var(--tc-reputation-1)'
})

const reputationStars = computed(() => {
  if (!otherUser.value) return ''
  return '★'.repeat(otherUser.value.reputation) + '☆'.repeat(5 - otherUser.value.reputation)
})

// Methods
const handleBackNavigation = () => {
  // Check if there's history to go back to
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    // Fallback to dashboard if no history
    router.push('/dashboard')
  }
}

const handleMenuClick = () => {
  // This would open a menu with options like:
  // - Report user
  // - Transaction details
  // - Help & Support
  console.log('Menu clicked')
}

const handleThemeToggle = () => {
  themeStore.toggleTheme()
}

const handlePaymentInfoClick = () => {
  if (isPaymentEditable.value) {
    // Open payment method selector for editing
    transactionalChatStore.performAction('paymentInfo')
  } else {
    // Show informational modal (read-only)
    console.log('Show payment info modal (read-only)')
    // TODO: Implement read-only payment info modal
  }
}

// Mobile touch event handling to prevent page scroll
const headerRef = ref<HTMLElement | null>(null)

const handleTouchStart = (event: TouchEvent) => {
  // Prevent scroll bubbling from header area
  event.stopPropagation()
}

const handleTouchMove = (event: TouchEvent) => {
  // Completely prevent scroll gestures on header
  event.preventDefault()
  event.stopPropagation()
}

const handleTouchEnd = (event: TouchEvent) => {
  // Clean up touch handling
  event.stopPropagation()
}

// Setup non-passive touch event listeners
onMounted(() => {
  if (headerRef.value) {
    // Use non-passive listeners to make events cancelable
    headerRef.value.addEventListener('touchstart', handleTouchStart, { passive: false })
    headerRef.value.addEventListener('touchmove', handleTouchMove, { passive: false })
    headerRef.value.addEventListener('touchend', handleTouchEnd, { passive: false })
  }
})

onBeforeUnmount(() => {
  if (headerRef.value) {
    headerRef.value.removeEventListener('touchstart', handleTouchStart)
    headerRef.value.removeEventListener('touchmove', handleTouchMove)
    headerRef.value.removeEventListener('touchend', handleTouchEnd)
  }
})
</script>

<template>
  <header
    ref="headerRef"
    class="header-bar"
    data-testid="header-bar"
  >
    <div class="header-content">
      <!-- Back Button -->
      <button 
        class="back-btn"
        data-testid="back-btn"
        @click="handleBackNavigation"
      >
        <span class="back-icon">←</span>
      </button>
      
      <!-- User Info -->
      <div 
        v-if="otherUser"
        class="user-info"
        data-testid="user-info"
      >
        <!-- Profile Picture -->
        <div class="profile-pic-container">
          <img 
            v-if="otherUser.profilePic"
            :src="otherUser.profilePic"
            :alt="otherUser.name"
            class="profile-pic"
            data-testid="profile-pic"
          />
          <div 
            v-else
            class="profile-pic-placeholder"
            data-testid="profile-pic-placeholder"
          >
            {{ otherUser.name.charAt(0).toUpperCase() }}
          </div>
        </div>
        
        <!-- User Details -->
        <div class="user-details">
          <h1 class="transaction-summary" data-testid="transaction-summary">
            {{ transactionSummary }}
          </h1>
         
          <div 
            class="reputation"
            :style="{ color: reputationColor }"
            data-testid="reputation"
          >
            <span class="reputation-stars">{{ reputationStars }}</span>
            <span class="reputation-text">
              {{ t('transactionalChat.header.reputation', { level: otherUser.reputation }) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- Loading Skeleton -->
      <div 
        v-else
        class="user-info-skeleton"
        data-testid="user-info-skeleton"
      >
        <div class="skeleton-avatar"></div>
        <div class="skeleton-details">
          <div class="skeleton-name"></div>
          <div class="skeleton-reputation"></div>
        </div>
      </div>
      
      <!-- Payment Info Button with Tooltip -->
      <CustomTooltip
        v-if="hasPaymentMethod || isPaymentEditable"
        :payment-info="paymentInfoForTooltip || undefined"
        placement="auto"
        trigger="hover"
        :disabled="!hasPaymentMethod"
        data-testid="payment-tooltip"
      >
        <template #trigger>
          <button 
            class="payment-info-btn"
            :class="{ 'readonly': !isPaymentEditable }"
            data-testid="payment-info-btn"
            @click="handlePaymentInfoClick"
          >
            <span class="payment-icon">
              {{ hasPaymentMethod ? '💳' : '💳' }}
            </span>
            <span v-if="paymentMethodSummary" class="payment-badge">
              {{ paymentMethodSummary.currency }}
            </span>
          </button>
        </template>
      </CustomTooltip>
      
      <!-- Theme Toggle Button -->
      <button 
        class="theme-btn"
        data-testid="theme-btn"
        @click="handleThemeToggle"
        :title="themeStore.currentTheme === 'light' ? 'Switch to Dark Mode' : 'Switch to Light Mode'"
      >
        <span class="theme-icon">
          {{ themeStore.currentTheme === 'light' ? '🌙' : '☀️' }}
        </span>
      </button>
      
    </div>
  </header>
</template>

<style scoped>
.header-bar {
  background-color: var(--tc-bg-header);
  border-bottom: 1px solid var(--tc-border-light);
  padding: 12px 16px;
  box-shadow: var(--tc-shadow-sm);
  z-index: 100;
  /* Prevent mobile scroll interference */
  touch-action: none;
  overscroll-behavior: contain;
  /* Prevent layout shifts */
  contain: layout style;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 100%;
}

/* Back Button */
.back-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: var(--tc-text-primary);
  transition: background-color 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background-color: var(--tc-bg-secondary);
}

.back-btn:active {
  background-color: var(--tc-border-light);
}

.back-icon {
  font-size: 18px;
  font-weight: bold;
}

/* User Info */
.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0; /* Allow shrinking */
}

.profile-pic-container {
  position: relative;
  flex-shrink: 0;
}

.profile-pic,
.profile-pic-placeholder {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-pic-placeholder {
  background: linear-gradient(135deg, var(--tc-primary), var(--tc-primary-hover));
  color: var(--tc-text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.user-details {
  flex: 1;
  min-width: 0; /* Allow text truncation */
}


.transaction-summary {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--tc-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.user-name {
  font-size: 13px;
  font-weight: 500;
  margin: 0;
  color: var(--tc-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.reputation {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 2px;
}

.reputation-stars {
  font-size: 12px;
  line-height: 1;
}

.reputation-text {
  font-size: 12px;
  font-weight: 500;
}

/* Loading Skeleton */
.user-info-skeleton {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

.skeleton-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--tc-border-light);
}

.skeleton-details {
  flex: 1;
}

.skeleton-name,
.skeleton-reputation {
  height: 14px;
  background-color: var(--tc-border-light);
  border-radius: 4px;
}

.skeleton-name {
  width: 60%;
  margin-bottom: 6px;
}

.skeleton-reputation {
  width: 40%;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Payment Info Button */
.payment-info-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 8px;
  color: var(--tc-text-primary);
  transition: all 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  flex-shrink: 0;
  position: relative;
}

.payment-info-btn:hover {
  background-color: var(--tc-bg-secondary);
  transform: translateY(-1px);
}

.payment-info-btn:active {
  background-color: var(--tc-border-light);
  transform: translateY(0);
}

.payment-info-btn.readonly {
  opacity: 0.7;
  cursor: default;
}

.payment-info-btn.readonly:hover {
  background-color: var(--tc-bg-secondary);
  transform: none;
}

.payment-icon {
  font-size: 16px;
}

.payment-badge {
  font-size: 10px;
  font-weight: 600;
  background: var(--tc-primary);
  color: var(--tc-text-white);
  padding: 2px 4px;
  border-radius: 4px;
  position: absolute;
  top: 2px;
  right: 2px;
  min-width: 20px;
  text-align: center;
}

/* Theme Button */
.theme-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: var(--tc-text-primary);
  transition: background-color 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.theme-btn:hover {
  background-color: var(--tc-bg-secondary);
}

.theme-btn:active {
  background-color: var(--tc-border-light);
}

.theme-icon {
  font-size: 18px;
}

/* Menu Button */
.menu-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: var(--tc-text-primary);
  transition: background-color 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.menu-btn:hover {
  background-color: var(--tc-bg-secondary);
}

.menu-btn:active {
  background-color: var(--tc-border-light);
}

.menu-icon {
  font-size: 18px;
  font-weight: bold;
  transform: rotate(90deg);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .header-bar {
    padding: 8px 12px;
  }
  
  .user-name {
    font-size: 15px;
  }
  
  .reputation-text {
    font-size: 11px;
  }
  
  .header-content {
    gap: 8px;
  }
}

/* RTL Support */
[dir="rtl"] .back-icon {
  transform: scaleX(-1);
}

[dir="rtl"] .header-content {
  direction: rtl;
}

[dir="rtl"] .user-details {
  text-align: right;
}
</style>
